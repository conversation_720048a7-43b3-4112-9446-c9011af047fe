#!/usr/bin/env python3
"""
Fix Bybit IP Restriction Issue
This script helps you update your Bybit API key IP whitelist
"""
import requests
import webbrowser
import time

def get_current_ip():
    """Get current public IP address"""
    try:
        response = requests.get('https://api.ipify.org', timeout=10)
        return response.text.strip()
    except Exception as e:
        print(f"❌ Error getting IP: {e}")
        return None

def main():
    print("🔧 Bybit IP Restriction Fix Tool")
    print("=" * 50)
    
    # Get current IP
    print("🌐 Detecting your current IP address...")
    current_ip = get_current_ip()
    
    if not current_ip:
        print("❌ Could not detect your IP address")
        return
    
    print(f"✅ Current IP: {current_ip}")
    print()
    
    # Show the issue
    print("📋 ISSUE DETECTED:")
    print("   Your IP address has changed since yesterday")
    print("   Previous IP: ************ (working)")
    print(f"   Current IP:  {current_ip} (blocked)")
    print()
    
    # Provide solution
    print("🔧 SOLUTION - Update Bybit API Key:")
    print("   1. Opening Bybit API management page...")
    
    # Open Bybit API management page
    try:
        webbrowser.open('https://www.bybit.com/app/user/api-management')
        print("   ✅ Browser opened to Bybit API management")
    except:
        print("   ⚠️ Please manually go to: https://www.bybit.com/app/user/api-management")
    
    print()
    print("   2. Find your API key and click 'Edit'")
    print("   3. In the IP whitelist section:")
    print(f"      - Add this IP: {current_ip}")
    print("      - OR disable IP restrictions (less secure)")
    print("   4. Save the changes")
    print("   5. Wait 1-2 minutes for changes to take effect")
    print()
    
    # Alternative solution
    print("💡 ALTERNATIVE - Disable IP Restrictions:")
    print("   If you have a dynamic IP that changes frequently,")
    print("   consider disabling IP restrictions entirely.")
    print("   This is less secure but more convenient.")
    print()
    
    # Wait for user action
    input("Press Enter after you've updated the IP whitelist...")
    
    # Test the connection
    print("🧪 Testing Bybit connection...")
    try:
        from dotenv import load_dotenv
        import os
        load_dotenv()
        
        from pybit.unified_trading import HTTP
        
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        if not api_key or not api_secret:
            print("❌ Bybit credentials not found in .env file")
            return
        
        session = HTTP(
            api_key=api_key,
            api_secret=api_secret,
            testnet=False,
            recv_window=10000
        )
        
        # Test connection
        account_info = session.get_account_info()
        
        if account_info.get('retCode') == 0:
            print("✅ SUCCESS! Bybit connection is now working!")
            print("🚀 You can now run the trading system")
        else:
            print(f"❌ Still not working: {account_info}")
            print("⏰ Wait a bit longer for changes to propagate")
            
    except Exception as e:
        if "Unmatched IP" in str(e):
            print("⏰ IP still not updated. Wait 1-2 minutes and try again.")
        else:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
